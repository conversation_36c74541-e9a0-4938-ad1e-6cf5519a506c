// mentor_dashboard.dart

import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'incoming_call.dart'; // Import the IncomingCall widget

class MentorDashboard extends StatefulWidget {
  const MentorDashboard({super.key});

  @override
  State<MentorDashboard> createState() => _MentorDashboardState();
}

class _MentorDashboardState extends State<MentorDashboard> {
  final String apiBaseUrl = 'https://testing.sasthra.in';
  final String livekitUrl = 'wss://livekit.sasthra.in';

  String? mentorId;
  String currentStatus = 'online';
  bool isStatusOpen = false;
  Map<String, dynamic>? mentorData;
  bool isLoading = true;
  String? errorMessage;
  bool isSessionLoading = false;
  Timer? pollingTimer;
  Map<String, dynamic>? incomingCall;
  bool showIncomingCall = false;
  bool isCallAccepting = false;
  final AudioPlayer ringtonePlayer = AudioPlayer();
  String currentTime = '';

  final List<Map<String, dynamic>> statusOptions = [
    {
      'value': 'online',
      'label': 'Online',
      'color': Colors.green[400],
      'pulseColor': Colors.green.withOpacity(0.7),
    },
    {
      'value': 'set_away',
      'label': 'Away',
      'color': Colors.yellow[400],
      'pulseColor': Colors.yellow.withOpacity(0.7),
    },
    {
      'value': 'offline',
      'label': 'Offline',
      'color': Colors.red[400],
      'pulseColor': Colors.red.withOpacity(0.7),
    },
  ];

  // Mock data for student progress
  final List<Map<String, dynamic>> studentProgress = [
    {'name': 'Alice Johnson', 'progress': 85, 'avatarColor': Colors.pink[500]},
    {'name': 'Bob Smith', 'progress': 72, 'avatarColor': Colors.blue[500]},
    {'name': 'Charlie Brown', 'progress': 93, 'avatarColor': Colors.green[500]},
    {'name': 'Diana Prince', 'progress': 68, 'avatarColor': Colors.purple[500]},
  ];

  // Mock upcoming sessions
  final List<Map<String, dynamic>> upcomingSessions = [
    {'title': 'Advanced React Patterns', 'time': 'Today, 4:00 PM', 'status': 'upcoming'},
    {'title': 'State Management Deep Dive', 'time': 'Wed, 4:00 PM', 'status': 'scheduled'},
    {'title': 'Project Review Session', 'time': 'Fri, 4:00 PM', 'status': 'scheduled'},
  ];

  @override
  void initState() {
    super.initState();
    _loadMentorData();
    _updateTime();
    Timer.periodic(const Duration(minutes: 1), (_) => _updateTime());
  }

  void _updateTime() {
    final now = DateTime.now();
    setState(() {
      currentTime = '${now.hour.toString().padStart(2, '0')}:${now.minute.toString().padStart(2, '0')}';
    });
  }

  Future<void> _loadMentorData() async {
    final prefs = await SharedPreferences.getInstance();
    mentorId = prefs.getString('user_id');
    if (mentorId == null) {
      setState(() {
        errorMessage = 'Mentor ID not found. Please log in again.';
        isLoading = false;
      });
      return;
    }

    // Automatically set to online on init
    await _handleStatusChange('online');

    // Fetch mentor data (simulate useDirectorMentorDashboardServiceQuery)
    try {
      final response = await http.get(Uri.parse('$apiBaseUrl/mentor-dashboard'));
      if (response.statusCode == 200) {
        setState(() {
          mentorData = jsonDecode(response.body);
          isLoading = false;
        });
      } else {
        setState(() {
          errorMessage = 'Failed to load mentor data.';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Error loading data: $e';
        isLoading = false;
      });
    }
  }

  Future<void> _handleStatusChange(String status) async {
    final prefs = await SharedPreferences.getInstance();
    final sessionId = prefs.getString('activeSessionId');
    if (sessionId == null) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('No active session found.')));
      return;
    }

    setState(() => isSessionLoading = true);
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/mentor-session'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'action': status, 'session_id': sessionId}),
      );
      if (response.statusCode == 200) {
        setState(() {
          currentStatus = status;
          isStatusOpen = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Status updated to $status')));
        if (status == 'online') {
          _startCallPolling();
        } else {
          _stopCallPolling();
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Failed to update status.')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error: $e')));
    } finally {
      setState(() => isSessionLoading = false);
    }
  }

  void _startCallPolling() {
    if (pollingTimer != null || mentorId == null) return;
    pollingTimer = Timer.periodic(const Duration(seconds: 5), (_) async {
      try {
        final response = await http.get(Uri.parse('$apiBaseUrl/mentor/poll?mentor_id=$mentorId'));
        final data = jsonDecode(response.body);
        if (data['incoming_call'] && data['room_name'] != null && data['student_id'] != null && incomingCall == null && !showIncomingCall) {
          setState(() {
            incomingCall = {
              'room_name': data['room_name'],
              'student_id': data['student_id'],
              'timestamp': data['timestamp'],
            };
            showIncomingCall = true;
          });
          // Play ringtone
          await ringtonePlayer.play(AssetSource('phone-ringtone-cabinet-356927.mp3'), volume: 1.0, isLocal: true);
        }
      } catch (e) {
        // Handle error
      }
    });
  }

  void _stopCallPolling() {
    pollingTimer?.cancel();
    pollingTimer = null;
  }

  Future<void> _handleAcceptCall() async {
    if (incomingCall == null || isCallAccepting || mentorId == null) return;
    ringtonePlayer.stop();
    setState(() => isCallAccepting = true);
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/call/accept'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'mentor_id': mentorId, 'room_name': incomingCall!['room_name']}),
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        setState(() {
          incomingCall = {...incomingCall!, 'token': data['token']};
          showIncomingCall = false;
        });
        // Navigate to IncomingCall widget
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => IncomingCall(
                mentorId: mentorId!,
                incomingCall: incomingCall,
                showVideoCall: true,
                onCallStatusChange: _handleCallStatusChange,
              ),
            ),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Failed to accept call.')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error: $e')));
    } finally {
      setState(() => isCallAccepting = false);
    }
  }

  Future<void> _handleRejectCall() async {
    if (incomingCall == null) return;
    ringtonePlayer.stop();
    try {
      await http.post(
        Uri.parse('$apiBaseUrl/call/end'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'room_name': incomingCall!['room_name']}),
      );
    } catch (e) {
      // Handle error
    }
    setState(() {
      incomingCall = null;
      showIncomingCall = false;
    });
  }

  void _handleCallStatusChange(String status, Map<String, dynamic>? callData) {
    if (status == 'ended' || status == 'declined') {
      setState(() {
        incomingCall = null;
        showIncomingCall = false;
        isCallAccepting = false;
      });
    }
  }

  @override
  void dispose() {
    _stopCallPolling();
    ringtonePlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentStatusConfig = statusOptions.firstWhere(
      (opt) => opt['value'] == currentStatus,
      orElse: () => statusOptions[0],
    );

    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (errorMessage != null) {
      return Center(child: Text(errorMessage!));
    }

    return Stack(
      children: [
        Scaffold(
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Welcome, Mentor', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                      if (currentStatus == 'online')
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(color: Colors.green[100], borderRadius: BorderRadius.circular(8)),
                          child: const Text('Ready for calls', style: TextStyle(color: Colors.green)),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Profile Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 30,
                                child: Text('${mentorData?['mendor']?['first_name']?.substring(0,1) ?? 'M'}'),
                              ),
                              const SizedBox(width: 16),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('${mentorData?['mendor']?['first_name'] ?? 'Mentor'} ${mentorData?['mendor']?['last_name'] ?? ''}'),
                                  const Text('Senior Mentor'),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          DropdownButton<String>(
                            value: currentStatus,
                            items: statusOptions.map((opt) => DropdownMenuItem(value: opt['value'], child: Text(opt['label']))).toList(),
                            onChanged: (value) => _handleStatusChange(value!),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Stats Grid
                  GridView.count(
                    crossAxisCount: 2,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      Card(child: Center(child: Text('Students: 42'))),
                      Card(child: Center(child: Text('Courses: 3'))),
                      Card(child: Center(child: Text('Sessions: 18'))),
                      Card(child: Center(child: Text('Rating: 4.8'))),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Course Details
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Course Information', style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          Text('Current Course: ${mentorData?['mendor']?['course_name'] ?? 'N/A'}'),
                          Text('Subject: ${mentorData?['mendor']?['subject_name'] ?? 'N/A'}'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Student Progress
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Student Progress', style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          ...studentProgress.map((student) => ListTile(
                                leading: CircleAvatar(backgroundColor: student['avatarColor'], child: Text(student['name'][0])),
                                title: Text(student['name']),
                                subtitle: LinearProgressIndicator(value: student['progress'] / 100),
                              )),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Upcoming Sessions
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Upcoming Sessions', style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          ...upcomingSessions.map((session) => ListTile(
                                title: Text(session['title']),
                                subtitle: Text(session['time']),
                              )),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (showIncomingCall && incomingCall != null)
          Center(
            child: AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Incoming Call', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                  Text('Student ID: ${incomingCall!['student_id']}'),
                  Text('Room: ${incomingCall!['room_name']}'),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(onPressed: _handleRejectCall, child: const Text('Decline'), style: ElevatedButton.styleFrom(backgroundColor: Colors.red)),
                      ElevatedButton(onPressed: _handleAcceptCall, child: const Text('Accept'), style: ElevatedButton.styleFrom(backgroundColor: Colors.green)),
                    ],
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}