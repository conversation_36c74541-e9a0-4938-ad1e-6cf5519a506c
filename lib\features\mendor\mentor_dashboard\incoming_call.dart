// incoming_call.dart

import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:livekit_client/livekit_client.dart';

class IncomingCall extends StatefulWidget {
  final String mentorId;
  final Map<String, dynamic>? incomingCall;
  final bool showVideoCall;
  final Function(String, Map<String, dynamic>?) onCallStatusChange;

  const IncomingCall({
    super.key,
    required this.mentorId,
    this.incomingCall,
    required this.showVideoCall,
    required this.onCallStatusChange,
  });

  @override
  State<IncomingCall> createState() => _IncomingCallState();
}

class _IncomingCallState extends State<IncomingCall> {
  final String apiBaseUrl = 'https://testing.sasthra.in';
  final String livekitUrl = 'wss://livekit.sasthra.in';

  String callState = 'idle';
  Room? room;
  List<Track> localTracks = [];
  bool isMuted = false;
  bool isVideoOff = false;
  bool isScreenSharing = false;
  int callDuration = 0;
  String error = '';
  String mediaError = '';
  Timer? callTimer;
  VideoTrack? localVideoTrack;
  AudioTrack? localAudioTrack;
  VideoTrack? remoteVideoTrack;
  AudioTrack? remoteAudioTrack;
  VideoTrack? remoteScreenTrack;
  AudioTrack? remoteScreenAudioTrack;
  bool isRemoteAudioMuted = false;

  final localVideoController = VideoTrackRendererController();
  final remoteVideoController = VideoTrackRendererController();
  final screenShareController = VideoTrackRendererController();

  @override
  void initState() {
    super.initState();
    if (widget.incomingCall != null && widget.incomingCall!['token'] != null) {
      _joinRoom(widget.incomingCall!['token']);
    }
  }

  void _startCallTimer() {
    callTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      setState(() => callDuration++);
    });
  }

  void _stopCallTimer() {
    callTimer?.cancel();
  }

  String _formatDuration(int seconds) {
    final mins = seconds ~/ 60;
    final secs = seconds % 60;
    return '${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}';
  }

  Future<void> _joinRoom(String token) async {
    setState(() => callState = 'connecting');
    try {
      final tracks = await Hardware.instance.createTracks(audio: true, video: true);
      localTracks = tracks;
      localVideoTrack = tracks.firstWhere((t) => t.kind == TrackKind.video) as VideoTrack?;
      localAudioTrack = tracks.firstWhere((t) => t.kind == TrackKind.audio) as AudioTrack?;

      room = Room();
      room!.addListener(_onRoomEvent);

      await room!.connect(livekitUrl, token);

      for (final track in tracks) {
        await room!.localParticipant!.publishTrack(track);
      }

      setState(() => callState = 'connected');
      _startCallTimer();
      widget.onCallStatusChange('connected', widget.incomingCall);
    } catch (e) {
      setState(() {
        error = 'Failed to connect: $e';
        callState = 'error';
      });
      widget.onCallStatusChange('error', widget.incomingCall);
    }
  }

  void _onRoomEvent() {
    // Handle events similar to React
    // For brevity, implement key events like TrackSubscribed, etc.
    // Use room.remoteParticipants to get remote tracks and attach to controllers
  }

  Future<void> _endCall() async {
    try {
      if (widget.incomingCall?['room_name'] != null) {
        await http.post(
          Uri.parse('$apiBaseUrl/call/end'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({'room_name': widget.incomingCall!['room_name']}),
        );
      }
    } catch (e) {
      // Handle error
    }
    _cleanup();
    setState(() => callState = 'ended');
    widget.onCallStatusChange('ended', widget.incomingCall);
  }

  void _cleanup() {
    room?.disconnect();
    room = null;
    for (final track in localTracks) {
      track.stop();
    }
    localTracks.clear();
    localVideoTrack = null;
    localAudioTrack = null;
    remoteVideoTrack = null;
    remoteAudioTrack = null;
    remoteScreenTrack = null;
    remoteScreenAudioTrack = null;
    _stopCallTimer();
  }

  Future<void> _toggleMute() async {
    if (localAudioTrack != null) {
      if (isMuted) {
        await localAudioTrack!.enable();
      } else {
        await localAudioTrack!.disable();
      }
      setState(() => isMuted = !isMuted);
    }
  }

  Future<void> _toggleVideo() async {
    if (localVideoTrack != null) {
      if (isVideoOff) {
        await localVideoTrack!.enable();
      } else {
        await localVideoTrack!.disable();
      }
      setState(() => isVideoOff = !isVideoOff);
    }
  }

  Future<void> _toggleScreenShare() async {
    if (room == null) return;
    if (isScreenSharing) {
      await room!.localParticipant!.setScreenShareEnabled(false);
    } else {
      await room!.localParticipant!.setScreenShareEnabled(true);
    }
    setState(() => isScreenSharing = !isScreenSharing);
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showVideoCall || widget.incomingCall == null) return const SizedBox.shrink();

    if (callState == 'connecting') {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (callState == 'connected') {
      return Scaffold(
        body: Column(
          children: [
            // Header with duration, etc.
            Container(
              color: Colors.grey[800],
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Connected | Student: ${widget.incomingCall!['student_id']}'),
                  Text('Duration: ${_formatDuration(callDuration)}'),
                ],
              ),
            ),
            Expanded(
              child: Row(
                children: [
                  // Remote Video
                  Expanded(child: VideoTrackRenderer(remoteVideoTrack)),
                  // Local Video
                  Expanded(child: VideoTrackRenderer(localVideoTrack)),
                ],
              ),
            ),
            if (isScreenSharing) Expanded(child: VideoTrackRenderer(remoteScreenTrack)),
            // Controls
            Container(
              color: Colors.grey[800],
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(onPressed: _toggleMute, icon: Icon(isMuted ? Icons.mic_off : Icons.mic)),
                  IconButton(onPressed: _toggleVideo, icon: Icon(isVideoOff ? Icons.videocam_off : Icons.videocam)),
                  IconButton(onPressed: _toggleScreenShare, icon: Icon(isScreenSharing ? Icons.screen_share : Icons.stop_screen_share)),
                  IconButton(onPressed: _endCall, icon: const Icon(Icons.call_end, color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return Scaffold(body: Center(child: Text(callState == 'error' ? error : 'Call Ended')));
  }
}