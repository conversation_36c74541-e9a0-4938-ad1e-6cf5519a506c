// incoming_call.dart

import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:livekit_client/livekit_client.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../core/config/app_config.dart';
import '../../../core/utils/logger.dart';

class IncomingCall extends StatefulWidget {
  final String mentorId;
  final Map<String, dynamic>? incomingCall;
  final bool showVideoCall;
  final Function(String, Map<String, dynamic>?) onCallStatusChange;

  const IncomingCall({
    super.key,
    required this.mentorId,
    this.incomingCall,
    required this.showVideoCall,
    required this.onCallStatusChange,
  });

  @override
  State<IncomingCall> createState() => _IncomingCallState();
}

class _IncomingCallState extends State<IncomingCall> {
  final String apiBaseUrl = AppConfig.baseUrl;
  final String livekitUrl = 'wss://livekit.sasthra.in';

  String callState = 'idle';
  Room? room;
  List<LocalTrackPublication> localTracks = [];
  bool isMuted = false;
  bool isVideoOff = false;
  bool isScreenSharing = false;
  int callDuration = 0;
  String error = '';
  String mediaError = '';
  Timer? callTimer;
  LocalVideoTrack? localVideoTrack;
  LocalAudioTrack? localAudioTrack;
  RemoteVideoTrack? remoteVideoTrack;
  RemoteAudioTrack? remoteAudioTrack;
  RemoteVideoTrack? remoteScreenTrack;
  RemoteAudioTrack? remoteScreenAudioTrack;
  bool isRemoteAudioMuted = false;
  RemoteParticipant? remoteParticipant;

  @override
  void initState() {
    super.initState();
    if (widget.incomingCall != null && widget.incomingCall!['token'] != null) {
      _requestPermissions().then((_) {
        _joinRoom(widget.incomingCall!['token']);
      }).catchError((e) {
        setState(() {
          error = 'Permission error: $e';
          callState = 'error';
        });
        AppLogger.error('Permission request failed: $e');
      });
    }
  }

  Future<void> _requestPermissions() async {
    try {
      var cameraStatus = await Permission.camera.status;
      if (!cameraStatus.isGranted) {
        cameraStatus = await Permission.camera.request();
        if (!cameraStatus.isGranted) {
          throw 'Camera permission denied';
        }
      }

      var micStatus = await Permission.microphone.status;
      if (!micStatus.isGranted) {
        micStatus = await Permission.microphone.request();
        if (!micStatus.isGranted) {
          throw 'Microphone permission denied';
        }
      }

      AppLogger.info('Permissions granted successfully');
    } catch (e) {
      AppLogger.error('Permission request error: $e');
      rethrow;
    }
  }

  void _startCallTimer() {
    callTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted) {
        setState(() => callDuration++);
      }
    });
  }

  void _stopCallTimer() {
    callTimer?.cancel();
  }

  String _formatDuration(int seconds) {
    final mins = seconds ~/ 60;
    final secs = seconds % 60;
    return '${mins.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  Future<void> _joinRoom(String token) async {
    if (!mounted) return;
    setState(() => callState = 'connecting');

    try {
      // Create room instance
      room = Room();

      // Set up room listeners
      room!.addListener(_onRoomEvent);

      // Set up specific event listeners
      room!.addListener(() {
        if (mounted) {
          _onRoomEvent();
        }
      });

      // Connect to room
      await room!.connect(
        livekitUrl,
        token,
        roomOptions: const RoomOptions(
          adaptiveStream: true,
          dynacast: true,
        ),
      );

      // Enable camera and microphone with error handling
      try {
        await room!.localParticipant?.setCameraEnabled(true);
      } catch (camError) {
        AppLogger.error('Failed to enable camera: $camError');
        setState(() => mediaError += 'Camera error: $camError\n');
      }

      try {
        await room!.localParticipant?.setMicrophoneEnabled(true);
      } catch (micError) {
        AppLogger.error('Failed to enable microphone: $micError');
        setState(() => mediaError += 'Microphone error: $micError\n');
      }

      // Get local tracks
      final videoPublications = room!.localParticipant?.videoTrackPublications
          .where((pub) => pub.source == TrackSource.camera)
          .toList() ?? [];
      if (videoPublications.isNotEmpty) {
        final track = videoPublications.first.track;
        if (track is LocalVideoTrack) {
          localVideoTrack = track;
        }
      }

      final audioPublications = room!.localParticipant?.audioTrackPublications
          .where((pub) => pub.source == TrackSource.microphone)
          .toList() ?? [];
      if (audioPublications.isNotEmpty) {
        final track = audioPublications.first.track;
        if (track is LocalAudioTrack) {
          localAudioTrack = track;
        }
      }

      if (mounted) {
        setState(() => callState = 'connected');
      }
      _startCallTimer();
      widget.onCallStatusChange('connected', widget.incomingCall);

      AppLogger.info('Successfully joined LiveKit room');

    } catch (e) {
      AppLogger.error('Failed to join room: $e');
      if (mounted) {
        setState(() {
          error = 'Failed to connect: $e';
          callState = 'error';
        });
      }
      widget.onCallStatusChange('error', widget.incomingCall);
    }
  }

  void _onRoomEvent() {
    if (room == null || !mounted) return;

    // Handle remote participants
    final participants = room!.remoteParticipants.values.toList();
    if (participants.isNotEmpty) {
      remoteParticipant = participants.first;

      // Get remote video track
      final videoPublications = remoteParticipant?.videoTrackPublications
          .where((pub) => pub.source == TrackSource.camera)
          .toList() ?? [];
      if (videoPublications.isNotEmpty) {
        final track = videoPublications.first.track;
        if (track is RemoteVideoTrack) {
          remoteVideoTrack = track;
        }
      }

      // Get remote audio track
      final audioPublications = remoteParticipant?.audioTrackPublications
          .where((pub) => pub.source == TrackSource.microphone)
          .toList() ?? [];
      if (audioPublications.isNotEmpty) {
        final track = audioPublications.first.track;
        if (track is RemoteAudioTrack) {
          remoteAudioTrack = track;
        }
      }

      // Get screen share track
      final screenPublications = remoteParticipant?.videoTrackPublications
          .where((pub) => pub.source == TrackSource.screenShareVideo)
          .toList() ?? [];
      if (screenPublications.isNotEmpty) {
        final track = screenPublications.first.track;
        if (track is RemoteVideoTrack) {
          remoteScreenTrack = track;
        }
      }

      if (mounted) {
        setState(() {});
      }
    }
  }

  Future<void> _endCall() async {
    try {
      if (widget.incomingCall?['room_name'] != null) {
        await http.post(
          Uri.parse('$apiBaseUrl/call/end'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({'room_name': widget.incomingCall!['room_name']}),
        );
      }
    } catch (e) {
      AppLogger.error('Error ending call via API: $e');
    }
    _cleanup();
    if (mounted) {
      setState(() => callState = 'ended');
    }
    widget.onCallStatusChange('ended', widget.incomingCall);
  }

  void _cleanup() {
    room?.disconnect();
    room = null;
    localTracks.clear();
    localVideoTrack = null;
    localAudioTrack = null;
    remoteVideoTrack = null;
    remoteAudioTrack = null;
    remoteScreenTrack = null;
    remoteScreenAudioTrack = null;
    remoteParticipant = null;
    _stopCallTimer();
  }

  Future<void> _toggleMute() async {
    if (room?.localParticipant != null) {
      try {
        await room!.localParticipant!.setMicrophoneEnabled(!isMuted);
        if (mounted) {
          setState(() => isMuted = !isMuted);
        }
        AppLogger.info('Microphone ${isMuted ? 'muted' : 'unmuted'}');
      } catch (e) {
        AppLogger.error('Error toggling mute: $e');
      }
    }
  }

  Future<void> _toggleVideo() async {
    if (room?.localParticipant != null) {
      try {
        await room!.localParticipant!.setCameraEnabled(!isVideoOff);
        if (mounted) {
          setState(() => isVideoOff = !isVideoOff);
        }
        AppLogger.info('Camera ${isVideoOff ? 'disabled' : 'enabled'}');
      } catch (e) {
        AppLogger.error('Error toggling video: $e');
      }
    }
  }

  Future<void> _toggleScreenShare() async {
    if (room?.localParticipant == null) return;

    try {
      if (isScreenSharing) {
        await room!.localParticipant!.setScreenShareEnabled(false);
      } else {
        await room!.localParticipant!.setScreenShareEnabled(true);
      }
      if (mounted) {
        setState(() => isScreenSharing = !isScreenSharing);
      }
      AppLogger.info('Screen share ${isScreenSharing ? 'started' : 'stopped'}');
    } catch (e) {
      AppLogger.error('Error toggling screen share: $e');
    }
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showVideoCall || widget.incomingCall == null) return const SizedBox.shrink();

    if (callState == 'connecting') {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (callState == 'error') {
      return Scaffold(body: Center(child: Text(error)));
    }

    if (callState == 'ended') {
      return const Scaffold(body: Center(child: Text('Call Ended')));
    }

    if (callState == 'connected') {
      return Scaffold(
        body: Column(
          children: [
            // Header with duration, etc.
            Container(
              color: Colors.grey[800],
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Connected | Student: ${widget.incomingCall!['student_id']}',
                            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue[600],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Duration: ${_formatDuration(callDuration)}',
                          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  if (remoteParticipant != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        'Participants: ${room?.remoteParticipants.length ?? 0 + 1}',
                        style: TextStyle(color: Colors.grey[300], fontSize: 12),
                      ),
                    ),
                ],
              ),
            ),
            if (mediaError.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(mediaError, style: const TextStyle(color: Colors.red)),
              ),
            Expanded(
              child: Row(
                children: [
                  // Remote Video
                  Expanded(
                    child: remoteVideoTrack != null
                        ? Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.blue, width: 2),
                            ),
                            child: VideoTrackRenderer(remoteVideoTrack!),
                          )
                        : Container(
                            color: Colors.black,
                            child: const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.person, color: Colors.white, size: 48),
                                  SizedBox(height: 8),
                                  Text('Waiting for remote video...', style: TextStyle(color: Colors.white)),
                                ],
                              ),
                            ),
                          ),
                  ),
                  // Local Video
                  Expanded(
                    child: localVideoTrack != null
                        ? Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.green, width: 2),
                            ),
                            child: VideoTrackRenderer(localVideoTrack!),
                          )
                        : Container(
                            color: Colors.grey[800],
                            child: const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.videocam_off, color: Colors.white, size: 48),
                                  SizedBox(height: 8),
                                  Text('Camera disabled', style: TextStyle(color: Colors.white)),
                                ],
                              ),
                            ),
                          ),
                  ),
                ],
              ),
            ),
            if (isScreenSharing && remoteScreenTrack != null)
              Expanded(child: VideoTrackRenderer(remoteScreenTrack!)),
            // Controls
            Container(
              color: Colors.grey[800],
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Mute/Unmute button
                  Container(
                    decoration: BoxDecoration(
                      color: isMuted ? Colors.red : Colors.green,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      onPressed: _toggleMute,
                      icon: Icon(
                        isMuted ? Icons.mic_off : Icons.mic,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  // Video on/off button
                  Container(
                    decoration: BoxDecoration(
                      color: isVideoOff ? Colors.red : Colors.green,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      onPressed: _toggleVideo,
                      icon: Icon(
                        isVideoOff ? Icons.videocam_off : Icons.videocam,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  // Screen share button
                  Container(
                    decoration: BoxDecoration(
                      color: isScreenSharing ? Colors.blue : Colors.grey[600],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      onPressed: _toggleScreenShare,
                      icon: Icon(
                        isScreenSharing ? Icons.stop_screen_share : Icons.screen_share,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  // End call button
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      onPressed: _endCall,
                      icon: const Icon(Icons.call_end, color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return const Scaffold(body: Center(child: Text('Unknown state')));
  }
}