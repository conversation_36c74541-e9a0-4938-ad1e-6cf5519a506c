import 'package:flutter/material.dart';
import '../core/config/app_config.dart';
import '../core/services/storage_service.dart';
import '../core/services/token_service.dart';
import '../features/mendor/mentor_dashboard/mentor_dashboard.dart';

class MentorDashboardDemo extends StatefulWidget {
  const MentorDashboardDemo({super.key});

  @override
  State<MentorDashboardDemo> createState() => _MentorDashboardDemoState();
}

class _MentorDashboardDemoState extends State<MentorDashboardDemo> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeDemo();
  }

  Future<void> _initializeDemo() async {
    try {
      // Initialize storage service
      await StorageService.init();
      
      // Set up demo user data
      await StorageService.setObject(AppConfig.userDataKey, {
        'id': 'mentor_123',
        'username': 'demo_mentor',
        'email': '<EMAIL>',
        'role': 'mendor',
        'first_name': '<PERSON>',
        'last_name': 'Doe',
      });

      // Set up demo token (this would normally come from login)
      final tokenService = TokenService();
      await tokenService.storeToken('demo_token_123');
      await tokenService.storeSessionId('demo_session_123');

      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      print('Error initializing demo: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Initializing Mentor Dashboard Demo...'),
              ],
            ),
          ),
        ),
      );
    }

    return MaterialApp(
      title: 'Mentor Dashboard Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Mentor Dashboard Demo'),
          backgroundColor: Colors.blue[600],
          foregroundColor: Colors.white,
        ),
        body: const MentorDashboard(),
      ),
    );
  }
}

void main() {
  runApp(const MentorDashboardDemo());
}
